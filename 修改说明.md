# 代理对比脚本修改说明

## 主要修改内容

### 1. 更改主要对比变量
- **原来**: 以账户名作为主要对比变量
- **现在**: 以账户ID作为主要对比变量

### 2. 数据结构调整
- **原来**: Map结构为 `账户名 -> 消耗金额`
- **现在**: Map结构为 `账户ID -> {consumption: 消耗金额, accountName: 账户名}`

### 3. 配置参数更新
- **明细表配置**:
  - 主键列关键词: `['账户ID', 'account id', '账户编号', 'customer id']`
  - 新增账户名列关键词: `['账户名', 'account name', '账户名称']`
  
- **代理表配置**:
  - 主键列关键词: `['账户ID', 'account id', '账户编号', 'customer id']`
  - 新增账户名列关键词: `['账户名', 'account name', '账户名称']`

### 4. 结果表格调整
- **列顺序**: 账户ID | 账户名 | 代理表消耗 | 明细表消耗 | 差额
- **格式设置**: 账户ID列设置为文本格式，防止长数字显示为科学记数法

### 5. 函数功能增强
- `createAgentConsumptionMap()`: 现在同时提取账户ID和账户名
- `createConsumptionMap()`: 支持嵌套和简单两种Map结构，都包含账户名信息
- 对比逻辑: 优先使用代理表中的账户名，如果没有则使用明细表中的账户名

### 6. 文档更新
- 更新了使用说明，明确指出需要账户ID列
- 更新了帮助文档，说明新的数据要求
- 添加了注释说明脚本现在以账户ID作为主要对比变量

## 使用注意事项

1. **明细表要求**:
   - 必须包含"代理"列
   - 必须包含"账户ID"列（或相关变体）
   - 必须包含"消耗"列
   - 可选包含"账户名"列

2. **代理表要求**:
   - 必须包含"账户ID"列（或相关变体）
   - 必须包含"消耗"列
   - 可选包含"账户名"列

3. **对比逻辑**:
   - 以账户ID为主键进行匹配
   - 如果同一账户ID有多条记录，会自动汇总消耗金额
   - 账户名信息用于显示，不影响对比逻辑

## Bug修复记录

### 修复的问题
1. **第14行错字**: "脚会自动" → "脚本会自动"
2. **第93行数据结构错误**: 直接访问Map值 → 访问对象的consumption属性
3. **变量命名不准确**: `allAccountNames` → `allAccountIds`

### 修复详情
- **错字修复**: 修正了使用说明中的错字
- **数据访问修复**: 修正了示例日志中对新数据结构的访问方式
- **变量重命名**: 将变量名改为更准确的描述，避免混淆

## 优势

1. **更准确的匹配**: 账户ID通常比账户名更稳定和唯一
2. **避免重名问题**: 不同账户可能有相同的账户名，但账户ID是唯一的
3. **更好的数据完整性**: 同时保留账户ID和账户名信息
4. **兼容性**: 支持只有账户ID或只有账户名的数据表
5. **代码健壮性**: 修复了所有发现的bug，确保代码稳定运行
