/**
 * @OnlyCurrentDoc
 *
 * 多代理消耗对比工具
 *
 * 使用说明：
 * 1. 确保电子表格中存在一个名为 "明细" 的工作表。
 *    - "明细" 表需要包含以下列: '代理', '账户ID', '消耗'。
 *    - '代理' 列可以通过 "数据工具" -> "一键处理" 生成。
 * 2. 确保存在一个或多个以 "代理-" 开头的工作表（例如 "代理-ET006"）。
 *    - 这些工作表是您从代理商处获得的原始数据。
 *    - 需要包含以下列: '账户ID' (或 '账户名'), '消耗'。
 * 3. 运行此脚本 (通过菜单 "代理对比工具" -> "开始对比")。
 * 4. 脚会自动为每个 "代理-" 表生成一个 "对比-" 表，显示与 "明细" 表的数据差异。
 * 注意：脚本现在以账户ID作为主要对比变量，确保数据准确性。
 */

// ==================== 菜单创建 ====================

/**
 * 创建自定义菜单
 */
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('代理对比工具')
    .addItem('▶️ 开始对比', 'compareMultipleAgentSheets')
    .addSeparator()
    .addItem('ℹ️ 帮助', 'showComparisonHelp')
    .addToUi();
}

/**
 * 帮助说明弹窗
 */
function showComparisonHelp() {
  const helpText =
    '【代理对比工具使用说明】\n\n' +
    '此工具用于对比 "代理-" 开头的工作表与 "明细" 表中的数据。\n\n' +
    '【前置条件】\n' +
    '1. 必须存在一个名为 "明细" 的工作表，且包含 "代理" 和 "账户ID" 列。\n' +
    '2. 必须存在一个或多个以 "代理-" 开头的工作表，且包含 "账户ID" 列。\n\n' +
    '【操作】\n' +
    '点击 "开始对比" 即可。脚本会自动识别代理并生成对比报告。\n' +
    '报告只显示有差异的数据，方便快速定位问题。';
  SpreadsheetApp.getUi().alert('帮助', helpText, SpreadsheetApp.getUi().ButtonSet.OK);
}


// ==================== 对比核心功能 ====================

function compareMultipleAgentSheets() {
  const ui = SpreadsheetApp.getUi();
  try {
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const allSheets = spreadsheet.getSheets();
    const baseSheetName = '明细';
    const baseSheet = spreadsheet.getSheetByName(baseSheetName);

    if (!baseSheet) {
      throw new Error(`找不到基准工作表: "${baseSheetName}"`);
    }

    const agentSheets = allSheets.filter(sheet => sheet.getName().includes('代理-'));

    if (agentSheets.length === 0) {
      throw new Error('未找到任何包含 "代理-" 的工作表。');
    }

    // Pre-process the "明细" sheet
    console.log('====== 步骤 1: 开始预处理 "明细" 表 ======');
    let baseDataByAgent;
    try {
      baseDataByAgent = createConsumptionMap(baseSheet, {
        primaryKeyCols: ['账户ID', 'account id', '账户编号', 'customer id'],
        consumptionCols: ['消耗', 'spent', 'cost', 'amount'],
        groupingKeyCols: ['代理', 'agent'],
        accountNameCols: ['账户名', 'account name', '账户名称']
      });
    } catch (error) {
      console.error('预处理"明细"表时发生错误:', error);
      throw new Error(`预处理"明细"表失败: ${error.message}`);
    }
    
    const detailAgentsFound = Array.from(baseDataByAgent.keys());
    console.log(`在 "明细" 表中找到 ${detailAgentsFound.length} 个代理: ${detailAgentsFound.join(', ')}`);
    
    if (detailAgentsFound.length > 0) {
        const firstAgent = detailAgentsFound[0];
        const firstAgentMap = baseDataByAgent.get(firstAgent);
        console.log(` -> 示例: 代理 "${firstAgent}" 下有 ${firstAgentMap.size} 个账户。`);
        if (firstAgentMap.size > 0) {
          const firstAccount = Array.from(firstAgentMap.keys())[0];
          console.log(` -> 示例账户: "${firstAccount}" 消耗总额: ${firstAgentMap.get(firstAccount)}`);
        }
        
        // 显示所有代理的Key，用于调试匹配问题
        console.log(' -> 所有明细表代理Key:', detailAgentsFound.map(key => `"${key}"`).join(', '));
    } else {
        console.warn('警告: 在"明细"表中未找到任何代理数据，请检查"代理"列是否存在且包含有效数据。');
    }
    console.log('============================================\n');


    const comparisonList = agentSheets.map(s => {
      const agentName = s.getName().match(/代理-([a-zA-Z0-9]+)/);
      return ` - ${s.getName()} vs. ${baseSheetName} (代理: ${agentName ? agentName[1] : '未识别'})`;
    }).join('\n');
    
    const response = ui.alert(
      '确认对比操作',
      `即将执行以下对比：\n\n${comparisonList}\n\n将为每个对比生成一个单独的工作表。是否继续？`,
      ui.ButtonSet.YES_NO
    );

    if (response !== ui.Button.YES) {
      throw new Error('用户取消了操作');
    }

    let comparisonsDone = 0;

    agentSheets.forEach(agentSheet => {
      const agentSheetName = agentSheet.getName();
      console.log(`\n====== 步骤 2: 开始处理工作表 "${agentSheetName}" ======`);
      
      try {
        // Use regex to robustly extract agent name, e.g., "ET006" from "消耗对账 - 代理-ET006"
        const match = agentSheetName.match(/代理-([a-zA-Z0-9]+)/);
        if (!match || !match[1]) {
          console.warn(` -> ❗️ 无法从工作表 "${agentSheetName}" 的名称中提取代理代码，已跳过。`);
          return; // 'return' inside a forEach is like 'continue' in a for loop
        }
        const agentNameFromSheet = normalizeKey(match[1]);
        console.log(` -> 从表名中提取到代理 Key: "${agentNameFromSheet}"`);
        
        const agentMap = createAgentConsumptionMap(agentSheet, {
          primaryKeyCols: ['账户ID', 'account id', '账户编号', 'customer id'],
          consumptionCols: ['消耗', 'spent', 'cost', 'amount', 'Amount spent (USD)'],
          accountNameCols: ['账户名', 'account name', '账户名称']
        });
        console.log(` -> 在 "${agentSheetName}" 表中找到 ${agentMap.size} 个账户。`);

        const baseMap = baseDataByAgent.get(agentNameFromSheet) || new Map();
        
        if (baseMap.size > 0) {
          console.log(` -> ✅ 在 "明细" 表数据中成功匹配到代理 "${agentNameFromSheet}"，包含 ${baseMap.size} 个账户。`);
        } else {
          console.log(` -> ❗️ 在 "明细" 表数据中未能匹配到代理 "${agentNameFromSheet}"！请检查 "明细" 表中的 "代理" 列是否存在该代理，并检查拼写/格式。`);
        }

        const allAccountNames = new Set([...Array.from(agentMap.keys()), ...Array.from(baseMap.keys())]);
        
        if (allAccountNames.size === 0) {
          console.log(` -> 跳过：没有找到任何账户数据可供对比。`);
          return;
        }
        
        const resultData = [['账户ID', '账户名', `${agentSheetName} 消耗`, `明细表 (${agentNameFromSheet}) 消耗`, '差额']];

        console.log(` -> 开始对比账户（共 ${allAccountNames.size} 个账户）...`);
        let matchedCount = 0;
        let onlyInAgentCount = 0;
        let onlyInDetailCount = 0;

        allAccountNames.forEach(accountId => {
          const agentData = agentMap.get(accountId) || { consumption: 0, accountName: '' };
          const baseData = baseMap.get(accountId) || { consumption: 0, accountName: '' };

          const agentConsumption = agentData.consumption || 0;
          const baseConsumption = baseData.consumption || 0;

          if (agentConsumption > 0 && baseConsumption > 0) matchedCount++;
          else if (agentConsumption > 0 && baseConsumption === 0) onlyInAgentCount++;
          else if (agentConsumption === 0 && baseConsumption > 0) onlyInDetailCount++;

          // 获取账户名，优先使用代理表中的账户名，如果没有则使用明细表中的
          const accountName = agentData.accountName || baseData.accountName || '';

          const difference = agentConsumption - baseConsumption;

          // Only include rows with a meaningful difference (e.g., > 1 cent)
          if (Math.abs(difference) >= 0.01) {
              resultData.push([
                accountId,
                accountName,
                Math.round(agentConsumption * 100) / 100,
                Math.round(baseConsumption * 100) / 100,
                Math.round(difference * 100) / 100
              ]);
          }
        });
        
        console.log(` -> 匹配统计: 双向匹配 ${matchedCount}个, 仅在代理表 ${onlyInAgentCount}个, 仅在明细表 ${onlyInDetailCount}个`);
        console.log('============================================');

        if (resultData.length > 1) {
          const headers = resultData.shift();
          // Sort by absolute difference, descending
          resultData.sort((a, b) => Math.abs(b[4]) - Math.abs(a[4]));
          resultData.unshift(headers);
          
          const resultSheetName = `对比-${agentNameFromSheet}`;
          const newSheet = getOrCreateSheet(spreadsheet, resultSheetName);
          newSheet.getRange(1, 1, resultData.length, resultData[0].length).setValues(resultData);
          
          // 设置账户ID列为文本格式防止科学记数法
          if (resultData.length > 1) {
              const accountIdRange = newSheet.getRange(2, 1, resultData.length - 1, 1);
              accountIdRange.setNumberFormat('@');
          }

          // 设置数字列格式
          if (resultData.length > 1) {
              const consumptionRange = newSheet.getRange(2, 3, resultData.length - 1, 3);
              consumptionRange.setNumberFormat('0.00');
          }

          newSheet.autoResizeColumns(1, resultData[0].length);
          console.log(`已生成对比表: ${resultSheetName}，包含 ${resultData.length - 1} 条差异记录`);
          comparisonsDone++;
        } else {
          console.log(`工作表 "${agentSheetName}" 与 "明细" 表中代理 "${agentNameFromSheet}" 的数据没有差异或没有可对比的数据，已跳过。`);
        }
        
      } catch (error) {
        console.error(`处理工作表 "${agentSheetName}" 时发生错误:`, error);
        // 继续处理下一个工作表，不终止整个流程
      }
    });

    if (comparisonsDone > 0) {
      ui.alert('成功', `已完成 ${comparisonsDone} 个对比。\n结果已保存至对应的 "对比-..." 工作表中。`, ui.ButtonSet.OK);
    } else {
      ui.alert('提示', '没有生成任何对比报告，请检查工作表内容和代理名称是否匹配。', ui.ButtonSet.OK);
    }

  } catch (error) {
    console.error('多表对比失败:', error);
    ui.alert('错误', `操作失败: ${error.message}`, ui.ButtonSet.OK);
  }
}


// ==================== 数据提取与处理函数 ====================

/**
 * Creates a consumption map specifically for agent sheets, summing up consumption by account ID.
 * This handles both detailed data (with dates) and pre-summarized data.
 *
 * @param {Sheet} sheet The Google Sheet object to process.
 * @param {object} config Configuration object.
 * @param {string[]} config.primaryKeyCols Keywords for the primary key column (e.g., '账户ID').
 * @param {string[]} config.consumptionCols Keywords for the consumption value column (e.g., '消耗').
 * @param {string[]} config.accountNameCols Keywords for the account name column (e.g., '账户名').
 * @returns {Map} A Map containing account ID -> {consumption, accountName}.
 */
function createAgentConsumptionMap(sheet, config) {
  const data = getSheetData(sheet);
  const result = new Map();
  if (data.length < 2) return result;

  const headers = data[0].map(h => String(h).trim());

  const primaryKeyIndex = findColumnIndex(headers, config.primaryKeyCols);
  const consumptionIndex = findColumnIndex(headers, config.consumptionCols);
  const accountNameIndex = config.accountNameCols ? findColumnIndex(headers, config.accountNameCols) : -1;

  if (primaryKeyIndex === -1) {
    console.error(`在工作表 "${sheet.getName()}" 中未找到主键列。尝试的关键词: ${config.primaryKeyCols.join(', ')}`);
    return result;
  }
  if (consumptionIndex === -1) {
    console.error(`在工作表 "${sheet.getName()}" 中未找到消耗列。尝试的关键词: ${config.consumptionCols.join(', ')}`);
    return result;
  }

  console.log(`  -> 找到列: 账户ID列(${primaryKeyIndex}) "${headers[primaryKeyIndex]}", 消耗列(${consumptionIndex}) "${headers[consumptionIndex]}"`);
  if (accountNameIndex !== -1) {
    console.log(`  -> 找到账户名列(${accountNameIndex}) "${headers[accountNameIndex]}"`);
  }

  for (let i = 1; i < data.length; i++) {
    const row = data[i];
    const primaryKey = normalizeKey(row[primaryKeyIndex]);
    if (!primaryKey) continue;

    const consumption = cleanNumberValue(row[consumptionIndex]);
    if (consumption === null) continue;

    const accountName = accountNameIndex !== -1 ? cleanStringValue(row[accountNameIndex]) : '';

    // Sum up consumption by account ID and keep account name
    const currentData = result.get(primaryKey) || { consumption: 0, accountName: '' };
    result.set(primaryKey, {
      consumption: currentData.consumption + consumption,
      accountName: accountName || currentData.accountName
    });
  }

  return result;
}

/**
 * Creates a map of consumption data from a given sheet.
 * Can create a simple map (primaryKey -> {consumption, accountName}) or a nested map (groupKey -> primaryKey -> {consumption, accountName}).
 *
 * @param {Sheet} sheet The Google Sheet object to process.
 * @param {object} config Configuration object.
 * @param {string[]} config.primaryKeyCols Keywords for the primary key column (e.g., '账户ID').
 * @param {string[]} config.consumptionCols Keywords for the consumption value column (e.g., '消耗').
 * @param {string[]} [config.groupingKeyCols] Optional. Keywords for the grouping key column (e.g., '代理').
 * @param {string[]} [config.accountNameCols] Optional. Keywords for the account name column (e.g., '账户名').
 * @returns {Map} A Map containing the processed data.
 */
function createConsumptionMap(sheet, config) {
  const data = getSheetData(sheet);
  const result = new Map();
  if (data.length < 2) return result;

  const headers = data[0].map(h => String(h).trim());

  const primaryKeyIndex = findColumnIndex(headers, config.primaryKeyCols);
  const consumptionIndex = findColumnIndex(headers, config.consumptionCols);
  const groupingKeyIndex = config.groupingKeyCols ? findColumnIndex(headers, config.groupingKeyCols) : -1;
  const accountNameIndex = config.accountNameCols ? findColumnIndex(headers, config.accountNameCols) : -1;

  if (primaryKeyIndex === -1) {
    console.error(`在工作表 "${sheet.getName()}" 中未找到主键列。尝试的关键词: ${config.primaryKeyCols.join(', ')}`);
    return result;
  }
  if (consumptionIndex === -1) {
    console.error(`在工作表 "${sheet.getName()}" 中未找到消耗列。尝试的关键词: ${config.consumptionCols.join(', ')}`);
    return result;
  }
  if (config.groupingKeyCols && groupingKeyIndex === -1) {
    console.error(`在工作表 "${sheet.getName()}" 中未找到分组列。尝试的关键词: ${config.groupingKeyCols.join(', ')}`);
    return result;
  }

  console.log(`  -> 找到列: 账户ID列(${primaryKeyIndex}) "${headers[primaryKeyIndex]}", 消耗列(${consumptionIndex}) "${headers[consumptionIndex]}"`);
  if (config.groupingKeyCols && groupingKeyIndex !== -1) {
    console.log(`  -> 找到代理列(${groupingKeyIndex}) "${headers[groupingKeyIndex]}"`);
  }
  if (accountNameIndex !== -1) {
    console.log(`  -> 找到账户名列(${accountNameIndex}) "${headers[accountNameIndex]}"`);
  }

  for (let i = 1; i < data.length; i++) {
    const row = data[i];
    const primaryKey = normalizeKey(row[primaryKeyIndex]);
    if (!primaryKey) continue;

    const consumption = cleanNumberValue(row[consumptionIndex]);
    if (consumption === null) continue;

    const accountName = accountNameIndex !== -1 ? cleanStringValue(row[accountNameIndex]) : '';

    if (groupingKeyIndex !== -1) {
      // Logic for nested map (e.g., from "明细" sheet)
      const groupKey = normalizeKey(row[groupingKeyIndex]);
      if (!groupKey) continue;

      if (!result.has(groupKey)) {
        result.set(groupKey, new Map());
      }
      const groupMap = result.get(groupKey);
      const currentData = groupMap.get(primaryKey) || { consumption: 0, accountName: '' };
      groupMap.set(primaryKey, {
        consumption: currentData.consumption + consumption,
        accountName: accountName || currentData.accountName
      });

    } else {
      // Logic for simple map (e.g., from "代理-XXX" sheets)
      const currentData = result.get(primaryKey) || { consumption: 0, accountName: '' };
      result.set(primaryKey, {
        consumption: currentData.consumption + consumption,
        accountName: accountName || currentData.accountName
      });
    }
  }
  return result;
}


// ==================== 共用工具函数 ====================

/**
 * 标准化键值，用于健壮的键匹配
 * 保留更多字符以支持账户ID格式如 "11ET006-0411-171"
 */
function normalizeKey(val) {
  if (!val) return '';
  // 只移除空格和特殊符号，保留字母、数字和连字符
  return String(val).trim().toLowerCase().replace(/[^\w-]/g, '');
}

/**
 * Finds the index of a column by checking for various keywords (case-insensitive and contains).
 * @param {string[]} headers - The array of header strings.
 * @param {string[]} keywords - An array of keywords to look for.
 * @returns {number} The found index, or -1 if not found.
 */
function findColumnIndex(headers, keywords) {
  const lowerCaseHeaders = headers.map(h => h.toLowerCase());
  for (const keyword of keywords) {
    const lowerKeyword = keyword.toLowerCase();
    // 先尝试精确匹配
    const exactIndex = lowerCaseHeaders.findIndex(h => h === lowerKeyword);
    if (exactIndex !== -1) {
      return exactIndex;
    }
  }
  
  // 如果精确匹配失败，再尝试包含匹配
  for (const keyword of keywords) {
    const lowerKeyword = keyword.toLowerCase();
    const index = lowerCaseHeaders.findIndex(h => h.includes(lowerKeyword));
    if (index !== -1) {
      return index;
    }
  }
  return -1;
}

/**
 * 获取或创建指定名称的工作表，如果存在则清空数据
 */
function getOrCreateSheet(spreadsheet, sheetName) {
  let sheet;
  
  try {
    // 尝试获取已存在的工作表
    sheet = spreadsheet.getSheetByName(sheetName);
    if (sheet) {
      console.log(`找到已存在的工作表: ${sheetName}，将清空数据`);
      // 清空工作表数据
      sheet.clear();
    }
  } catch (error) {
    // 工作表不存在
    sheet = null;
  }
  
  if (!sheet) {
    console.log(`创建新工作表: ${sheetName}`);
    sheet = spreadsheet.insertSheet(sheetName);
  }
  
  return sheet;
}

/**
 * 获取工作表的所有数据
 */
function getSheetData(sheet) {
  const lastRow = sheet.getLastRow();
  const lastCol = sheet.getLastColumn();
  
  if (lastRow === 0 || lastCol === 0) {
    return [];
  }
  
  const range = sheet.getRange(1, 1, lastRow, lastCol);
  // 使用 getDisplayValues() 来获取单元格显示的文本，防止长数字ID被转为科学记数法
  return range.getDisplayValues();
}

/**
 * 清理字符串值
 */
function cleanStringValue(val) {
  if (val === null || val === undefined) {
    return '';
  }
  
  const strVal = String(val).trim();
  if (strVal === 'nan' || strVal === 'null' || strVal === 'undefined') {
    return '';
  }
  
  return strVal;
}

/**
 * 清理数字值
 */
function cleanNumberValue(val) {
  if (val === null || val === undefined || val === '') {
    return null;
  }
  
  try {
    let cleanVal = val;
    
    // 如果是字符串，移除逗号、引号、空格、$符号等
    if (typeof val === 'string') {
      cleanVal = val.replace(/[,"\s$￥]/g, '');
      if (cleanVal === '' || cleanVal === '0' || cleanVal === '-') {
        return null;
      }
    }
    
    const numVal = Number(cleanVal);
    
    // 检查是否为有效数字且大于0
    if (isNaN(numVal) || numVal <= 0) {
      return null;
    }
    
    return numVal;
    
  } catch (error) {
    // 不要在这里输出警告，避免控制台过于混乱
    return null;
  }
} 